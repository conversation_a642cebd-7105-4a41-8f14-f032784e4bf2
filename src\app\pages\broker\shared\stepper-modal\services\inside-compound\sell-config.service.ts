import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  FLOOR_TYPES_OPTIONS,
  UNIT_VIEW_TYPES_OPTIONS,
  FINISHING_STATUS_TYPES_OPTIONS,
  DELIVERY_STATUS_TYPES_OPTIONS,
  OTHER_ACCESSORIES_OPTIONS,
  FINANCIAL_STATUS_TYPES_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root'
})
export class SellConfigService extends BaseConfigService {

  /**
   * Create sell-specific location inputs with additional fields for selling
   */
  private createSellLocationInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 2,
        name: 'compoundName',
        type: 'text',
        label: 'Compound Name',
        validators: [Validators.required],
        visibility: () => stepperModal.getInsideCompoundPrivilege(),
      },
      {
        step: 2,
        name: 'detailedAddress',
        type: 'text',
        label: 'Detailed Address',
        validators: [Validators.required],
        visibility: () => stepperModal.getSellInsideCompoundInputs(),
      },
      {
        step: 2,
        name: 'addressLink',
        type: 'url',
        label: 'Detailed Address Link',
        validators: [
          Validators.pattern(
            /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w .-]*)*\/?$/
          )
        ],
        visibility: () => stepperModal.getSellInsideCompoundInputs(),
      },
      {
        step: 2,
        name: 'projectManagement',
        type: 'text',
        label: 'Project Management',
        validators: [Validators.required],
        visibility: () => stepperModal.getSellInsideCompoundInputs(),
      },
      {
        step: 2,
        name: 'projectConstructor',
        type: 'text',
        label: 'Project Constructor',
        validators: [Validators.required],
        visibility: () => stepperModal.getSellInsideCompoundInputs(),
      },
      {
        step: 2,
        name: 'locationSuggestions',
        type: 'checkbox',
        label: 'Location Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 2,
        name: 'cityId',
        type: 'select',
        label: 'City',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'areaId',
        type: 'select',
        label: 'Area',
        options: [],
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 2,
        name: 'subAreaId',
        type: 'select',
        label: 'Sub Area',
        options: [],
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell-specific unit information for apartments and similar units
   */
  private createSellApartmentUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }

  /**
   * Create sell-specific unit information for duplexes and similar units
   */
  private createSellDuplexUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }

  private createSellVillasUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }

  private createSellTwinHousesUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }

  private createSellPenthousesUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }

  private createSellPAdministrativeUnitsUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'unitArea',
        type: 'number',
        label: 'Unit Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingNumber',
        type: 'text',
        label: 'Building Number',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'floor',
        type: 'select',
        label: 'Floor',
        options: FLOOR_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }

  private createSellCommercialAdministrativeBuildingsInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {
    const inputs: InputConfig[] = [
      {
        step: 3,
        name: 'unitNumber',
        type: 'text',
        label: 'Unit Number',
        validators: [Validators.required],
        visibility: () => true,
        isDynamic: true,
      },
      {
        step: 3,
        name: 'groundArea',
        type: 'number',
        label: 'Ground Area (m²)',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'buildingArea',
        type: 'number',
        label: 'Building Area (m²)',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'numberOfFloors',
        type: 'number',
        label: 'Number of Floors',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
    ];

    if (includeRooms) {
      inputs.push(
        {
          step: 3,
          name: 'rooms',
          type: 'number',
          label: 'Number of Rooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        },
        {
          step: 3,
          name: 'bathRooms',
          type: 'number',
          label: 'Number of Bathrooms',
          validators: [Validators.required, Validators.min(0)],
          visibility: () => true,
        }
      );
    }

    inputs.push(
      {
        step: 3,
        name: 'unitView',
        type: 'select',
        label: 'View',
        options: UNIT_VIEW_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'finishingStatus',
        type: 'select',
        label: 'Finishing Status',
        options: FINISHING_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryStatus',
        type: 'select',
        label: 'Delivery Status',
        options: DELIVERY_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'deliveryDate',
        type: 'text',
        label: 'Delivery Date',
        validators: [],
        visibility: () => {
          const formValue = stepperModal.getCurrentForm().value;
          return formValue?.deliveryStatus === 'under_construction';
        },
      },
      {
        step: 3,
        name: 'financialStatus',
        type: 'select',
        label: 'Financial Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'clubStatus',
        type: 'select',
        label: 'Club Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'parkingStatus',
        type: 'select',
        label: 'Parking Status',
        options: FINANCIAL_STATUS_TYPES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'otherAccessories',
        type: 'multiSelect',
        label: 'Other Accessories',
        options: OTHER_ACCESSORIES_OPTIONS,
        validators: [],
        visibility: () => true,
      },
      {
        step: 3,
        name: 'notes',
        type: 'textarea',
        label: 'Notes',
        validators: [],
        visibility: () => true,
      }
    );

    return inputs;
  }
  /**
   * Create sell-specific document inputs
   */
  private createSellDocumentInputs(): InputConfig[] {
    return [
      {
        step: 4,
        name: 'mainImage',
        type: 'file',
        label: 'Unit Main Image',
        validators: [Validators.required],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'unitInMasterPlanImage',
        type: 'file',
        label: 'Unit in Master Plan Image',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'galleryImages',
        type: 'file',
        label: 'Unit Gallery Images',
        validators: [],
        visibility: () => true,
      },
      {
        step: 4,
        name: 'video',
        type: 'file',
        label: 'Videos',
        validators: [],
        visibility: () => true,
      },
    ];
  }

  /**
   * Create sell-specific financial inputs
   */
  private createSellFinancialInputs(stepperModal: any): InputConfig[] {
    return [
      {
        step: 5,
        name: 'askingPrice',
        type: 'number',
        label: 'Asking Price',
        validators: [Validators.required, Validators.min(0)],
        visibility: () => true,
      },
      {
        step: 5,
        name: 'unitPriceSuggestions',
        type: 'checkbox',
        label: 'Price Suggestions',
        validators: [],
        visibility: () => stepperModal.isClient(),
      },
      {
        step: 5,
        name: 'requestedOver',
        type: 'number',
        label: 'Request Over',
        validators: [Validators.min(0)],
        visibility: () => true,
      },
    ];
  }

  /**
   * Configuration builder for primary inside compound sell apartments
   */
  private createPrimaryInsideCompoundSellApartmentsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellApartmentUnitInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary and resale inside compound sell duplexes
   */
  private createInsideCompoundSellDuplexesConfig(stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellDuplexUnitInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  private createInsideCompoundSellVillasConfig(stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellVillasUnitInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  private createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellTwinHousesUnitInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  private createPrimaryInsideCompoundPurchasingPenthouses(stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellPenthousesUnitInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  private createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellPAdministrativeUnitsUnitInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  private createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {
    return [
      ...this.createSellLocationInputs(stepperModal),
      ...this.createSellCommercialAdministrativeBuildingsInformationInputs(stepperModal, true),
      ...this.createSellDocumentInputs(),
      ...this.createSellFinancialInputs(stepperModal),
    ];
  }

  /**
   * Get input configurations for sell cases
   */
  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      {
        key: 'primary_inside_compound_sell_apartments',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_apartments',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_duplexes',
        value: this.createInsideCompoundSellDuplexesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_duplexes',
        value: this.createInsideCompoundSellDuplexesConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_studios',
        value: this.createInsideCompoundSellDuplexesConfig(stepperModal, {
          includeRooms: false,
          includeDocuments: true
        }),
      },
      {
        key: 'resale_inside_compound_sell_studios',
        value: this.createInsideCompoundSellDuplexesConfig(stepperModal, {
          includeRooms: false,
          includeDocuments: true
        }),
      },
      {
        key: 'primary_inside_compound_sell_villas',
        value: this.createInsideCompoundSellVillasConfig(stepperModal, {
          includeRooms: false,
          includeDocuments: true
        }),
      },
      {
        key: 'resale_inside_compound_sell_villas',
        value: this.createInsideCompoundSellVillasConfig(stepperModal, {
          includeRooms: false,
          includeDocuments: true
        }),
      },
      {
        key: 'primary_inside_compound_sell_administrative_units',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_administrative_units',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_medical_clinics',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_medical_clinics',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_pharmacies',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_pharmacies',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_shops',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_shops',
        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_twin_houses',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_twin_houses',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_town_houses',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_town_houses',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_standalone_villas',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_standalone_villas',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_penthouses',
        value: this.createPrimaryInsideCompoundPurchasingPenthouses(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_penthouses',
        value: this.createPrimaryInsideCompoundPurchasingPenthouses(stepperModal),
      },

      {
        key: 'primary_inside_compound_sell_administrative_units',
        value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_administrative_units',
        value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal),
      },

       {
        key: 'primary_inside_compound_sell_commercial_administrative_buildings',
        value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal),
      },
      {
        key: 'resale_inside_compound_sell_commercial_administrative_buildings',
        value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal),
      },


    ];
  }
}
