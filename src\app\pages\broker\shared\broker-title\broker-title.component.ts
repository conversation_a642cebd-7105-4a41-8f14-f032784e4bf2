import { BrokerService } from './../../services/broker.service';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { AccountTypeMapper } from '../../account-type-mapper';
import { AuthenticationService } from 'src/app/pages/authentication';

@Component({
  selector: 'app-broker-title',
  templateUrl: './broker-title.component.html',
  styleUrl: './broker-title.component.scss',
})

export class BrokerTitleComponent implements OnInit {
  @Input() showCreateButton: boolean = true;
  user: any = {};

  constructor(
    protected cd: ChangeDetectorRef,
    private brokerService: BrokerService,
    private authenticationService:AuthenticationService
  ) {}

  ngOnInit(): void {
    // let currentUser = localStorage.getItem('currentUser');
    // let userId = currentUser ? JSON.parse(currentUser).id : null;
    // this.getUser(userId);
    this.user = this.authenticationService.getSessionUser();
  }

  getUser(id: any) {
    this.brokerService.getById(id).subscribe({
      next: (response: any) => {
        console.log(response);
        this.user = response?.data ?? {};
        this.cd.detectChanges();
      },
      error: (error: any) => {
        Swal.fire(error.error.message, '', 'error');
      },
    });
  }

  capitalizeWords(text: string | null): string {
    if (!text) return '';
    return text.replace(/\b\w/g, (char) => char.toUpperCase());
  }

  getAccountTypeBadge(type: string): string {
    return AccountTypeMapper.getAccountTypeBadge(type);
  }
}
