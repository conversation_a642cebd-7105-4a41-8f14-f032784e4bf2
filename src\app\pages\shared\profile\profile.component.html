<!-- Loading State for Entire Page -->
<div *ngIf="isLoading" class="profile-loading-container d-flex justify-content-center align-items-center">
  <div class="text-center">
    <div class="spinner-border loading-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="loading-text">
      Loading Profile...
    </div>
  </div>
</div>

<!-- Profile Content - Show when not loading -->
<div *ngIf="!isLoading" class="profile-content">
  <app-profile-header [user]="user"></app-profile-header>

  <!-- profile details -->
  <app-profile-details [user]="user" [isLoading]="isLoading"></app-profile-details>

  <!-- Sign-in Method -->
  <app-sign-in-method [user]="user"></app-sign-in-method>

  <app-account-details *ngIf="user?.role === 'broker'" [title]="accountTitle"></app-account-details>

  <app-advertisements-details *ngIf="user?.role === 'broker'" [user]="user"></app-advertisements-details>
</div>
