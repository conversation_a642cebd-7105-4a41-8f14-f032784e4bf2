import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges, OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import Swal from 'sweetalert2';
import { ProfileService } from '../../services/profile.service';
import { BaseLoading } from '../../../base-loading/base-loading';

@Component({
  selector: 'app-sign-in-method',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './sign-in-method.component.html',
  styleUrl: './sign-in-method.component.scss',
})
export class SignInMethodComponent extends BaseLoading implements OnChanges {
  @Input() user: any = {};
  @Output() saveEmailEvent = new EventEmitter<any>();
  @Output() savePasswordEvent = new EventEmitter<any>();

  showChangeEmailForm: boolean = false;
  showChangePasswordForm: boolean = false;

  newEmail: string = '';
  currentPassword: string = '';
  newPassword: string = '';
  confirmPassword: string = '';

  localLoading: boolean = false;
  formData: any = {};

  constructor(
    private profileService: ProfileService,
    private cd: ChangeDetectorRef
  ) {
    super();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['user'] && this.user) {
      this.formData = { ...this.user };
    }
  }

  toggleEmailForm(show: boolean) {
    this.showChangeEmailForm = show;
    if (show && this.user.email) {
      this.newEmail = this.user.email;
    }
  }

  saveEmail() {
    if (!this.newEmail) {
      Swal.fire('Error', 'Email cannot be empty', 'error');
      return;
    }

    this.localLoading = true;

    const userData = {
      fullName: this.user.fullName,
      phone: this.user.phone,
      email: this.newEmail,
    };

    this.profileService.updateProfile(this.user.id, userData).subscribe({
      next:async (response) => {
        this.user.email = this.newEmail;
        this.localLoading = false;
        this.showChangeEmailForm = false;
        this.cd.detectChanges();
        localStorage.setItem('currentUser', JSON.stringify(response.data));

       await Swal.fire('Success', 'Email updated successfully', 'success');
       window.location.reload();


      },
      error: (error) => {
        this.localLoading = false;
        console.error('Error updating email:', error);

        let errorMessage = 'Failed to update email';
        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        }

        Swal.fire('Error', errorMessage, 'error');
      },
    });
  }

  togglePasswordForm(show: boolean) {
    this.showChangePasswordForm = show;
    if (!show) {
      this.currentPassword = '';
      this.newPassword = '';
      this.confirmPassword = '';
    }
  }

  savePassword() {
    if (!this.newPassword || !this.confirmPassword) {
      Swal.fire('Error', 'Please enter all password fields', 'error');
      return;
    }

    if (this.newPassword !== this.confirmPassword) {
      Swal.fire('Error', 'Passwords do not match', 'error');
      return;
    }

    this.localLoading = true;

    const userData = {
      password: this.newPassword,
      password_confirmation: this.confirmPassword,
    };

    this.profileService.updateProfile(this.user.id, userData).subscribe({
      next: (response) => {
        this.localLoading = false;
        this.showChangePasswordForm = false;
        this.cd.detectChanges();

        this.newPassword = '';
        this.confirmPassword = '';

        Swal.fire('Success', 'Password updated successfully', 'success');
      },
      error: (error) => {
        this.localLoading = false;
        console.error('Error updating password:', error);

        let errorMessage = 'Failed to update password';
        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        }

        Swal.fire('Error', errorMessage, 'error');
      },
    });
  }
}
