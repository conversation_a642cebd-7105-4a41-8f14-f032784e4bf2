// Simple Loading Styles
.profile-loading-container {
  min-height: 100vh;
  height: 100%;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;

  .loading-spinner {
    width: 3rem;
    height: 3rem;
    border-width: 3px;
    border-color: #007bff;
    border-right-color: transparent;
  }

  .loading-text {
    color: #495057;
    font-size: 1rem;
    margin-top: 1rem;
    font-weight: 400;
  }
}

// Simple fade in for content
.profile-content {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
