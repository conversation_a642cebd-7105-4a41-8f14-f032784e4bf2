import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { createrequestservice } from '../../services/createrequest.service';
import { PropertyService } from '../../services/property.service';
import { Router } from '@angular/router';
import { BehaviorSubject, forkJoin, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import Swal from 'sweetalert2';
import {
  STEPPER_CONFIG,
  SPECIALIZATION_SCOPE_OPTIONS,
  TYPE_OPTIONS,
  FILE_VALIDATION_CONFIG,
  OptionItem
} from './stepper-modal.constants';
import { StepperInputConfigService } from './stepper-input-config.service';

@Component({
  selector: 'app-stepper-modal',
  templateUrl: './stepper-modal.component.html',
  styleUrls: ['./stepper-modal.component.scss'],
})
export class StepperModalComponent implements OnInit {
  totalSteps = STEPPER_CONFIG.TOTAL_STEPS;
  currentStep = STEPPER_CONFIG.INITIAL_STEP;
  userId: number;
  userRole: string;
  stepForms: FormGroup[] = [];
  currentInputs: any[] = [];
  validationErrors: any[] = [];
  showErrorList = false;

  private citiesSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);
  private areasSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);
  private subAreasSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);

  cities$ = this.citiesSubject.asObservable();
  areas$ = this.areasSubject.asObservable();
  subAreas$ = this.subAreasSubject.asObservable();

  selectedCityId: number | null = null;
  selectedCityName: string = '';
  selectedAreaId: number | null = null;
  selectedAreaName: string = '';
  selectedSubAreaName: string = '';
  isLoadingCities = false;
  isSubmitting = false;

  specializationScopeOptions = SPECIALIZATION_SCOPE_OPTIONS;
  typeOptions = TYPE_OPTIONS;
  unitTypeOptions: OptionItem[] = [];
  fieldToStepMap: { [key: string]: number } = {};
  stepNames = STEPPER_CONFIG.STEP_NAMES;

  // Enhanced multiselect properties
  private searchQueries: { [fieldName: string]: string } = {};

  constructor(
    private fb: FormBuilder,
    private createRequestService: createrequestservice,
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private stepperInputConfigService: StepperInputConfigService
  ) {}

  ngOnInit(): void {
    const userJson = localStorage.getItem('currentUser');
    const user = userJson ? JSON.parse(userJson) : null;
    this.userId = user?.id;
    this.userRole = user?.role;

    this.initForms();
    this.loadInitialData();
    this.loadStepInputs();
  }

  initForms(): void {
    this.stepForms = Array(this.totalSteps)
      .fill(null)
      .map(() => this.fb.group({}));
    this.stepForms[0] = this.fb.group({
      specializationScope: ['', Validators.required],
      type: ['', Validators.required],
      unitType: ['', Validators.required],
    });
  }

  loadInitialData(): void {
    this.isLoadingCities = true;
    forkJoin({
      cities: this.propertyService.getCities().pipe(
        map((response: any) => {
          const cities = response.data || response;
          console.log('Cities response:', response);
          return cities.map((city: any) => ({
            key: city.name_en,
            value: city.id,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching cities:', error);
          Swal.fire('Error', 'Failed to fetch cities.', 'error');
          return of([]);
        })
      ),
      unitTypes: this.propertyService.getUnitTypes().pipe(
        map((response: any) => {
          const unitTypes = response.data || response;
          console.log('Unit types response:', response);
          return Object.entries(unitTypes).map(([key, value]) => ({
            key,
            value: value as string,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching unit types:', error);
          Swal.fire('Error', 'Failed to fetch unit types.', 'error');
          return of([]);
        })
      ),
    }).subscribe({
      next: ({ cities, unitTypes }) => {
        this.citiesSubject.next(cities);
        this.unitTypeOptions = unitTypes;
        this.isLoadingCities = false;
        this.cdr.markForCheck();
      },
      error: (err) => {
        this.isLoadingCities = false;
        console.error('Error loading initial data:', err);
        Swal.fire('Error', 'Failed to load initial data.', 'error');
        this.cdr.markForCheck();
      },
    });
  }

  loadStepInputs(): void {
    if (this.currentStep > 1) {
      this.currentInputs = this.stepperInputConfigService.getInputsForKey(this.getConfigKey(), this.currentStep, this);
      const formControls = this.currentInputs.reduce((acc: any, input: any) => {
        acc[input.name] = [input.type === 'multiSelect' ? [] : '', input.validators || []];
        return acc;
      }, {});
      this.stepForms[this.currentStep - 1] = this.fb.group(formControls);
      if (this.currentStep === 2) {
        this.stepForms[1].addControl('cityId', this.fb.control('', Validators.required));
        this.stepForms[1].addControl('areaId', this.fb.control('', Validators.required));
        this.stepForms[1].addControl('subAreaId', this.fb.control(''));
        this.stepForms[1].addControl(
          'compoundName',
          this.fb.control('', this.getInsideCompoundPrivilege() ? Validators.required : null)
        );

        // Add additional fields for outside compound scenarios
        if (!this.getInsideCompoundPrivilege()) {
          // Add detailedAddress and addressLink for rent-out and sell outside compound scenarios
          if (this.getRentOutsideCompoundInputs() || this.getSellOutsideCompoundInputs()) {
            this.stepForms[1].addControl('detailedAddress', this.fb.control('', Validators.required));
            this.stepForms[1].addControl('addressLink', this.fb.control(''));
          }
          // locationSuggestions is handled by the configuration services for all outside compound scenarios
        }
      }
    } else {
      this.currentInputs = [
        {
          name: 'specializationScope',
          type: 'select',
          label: 'Specialization Scope',
          options: this.specializationScopeOptions,
          validators: [Validators.required],
        },
        {
          name: 'type',
          type: 'select',
          label: 'Type',
          options: this.getFilteredTypeOptions(),
          validators: [Validators.required],
        },
        {
          name: 'unitType',
          type: 'select',
          label: 'Unit Type',
          options: this.getFilteredUnitTypeOptions(),
          validators: [Validators.required],
        },
      ];
    }
    this.cdr.markForCheck();
  }

  getSellInsideCompoundInputs(): boolean {

    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'resale_inside_compound',
      'primary_inside_compound',
    ];
    const typeScopes = [
      'sell',
      'purchasing',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getRentOutInsideCompoundInputs(): boolean {

    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'rentals_inside_compound',
    ];
    const typeScopes = [
      'rent_out',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getRentOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'rentals_outside_compound',
    ];
    const typeScopes = [
      'rent_out',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getRentInOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'rentals_outside_compound',
    ];
    const typeScopes = [
      'rent_in',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getSellOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'purchase_sell_outside_compound',
    ];
    const typeScopes = [
      'sell',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getPurchaseOutsideCompoundInputs(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    const targetScopes = [
      'purchase_sell_outside_compound',
    ];
    const typeScopes = [
      'purchasing',
    ];

    return targetScopes.includes(scope) && typeScopes.includes(type);
  }

  getMallPrivilege(): boolean {
    const type = this.stepForms[0].get('type')?.value;
    const unitType = this.stepForms[0].get('unitType')?.value;
    const mallUnitTypes = ['administrative_units', 'medical_clinics', 'pharmacies', 'commercial_stores'];
    const hasTargetType = ['sell', 'rent_out'].includes(type);
    return mallUnitTypes.includes(unitType) && hasTargetType && !this.getInsideCompoundPrivilege();
  }

  getUnitAndBuildingNumber(): boolean {
    const type = this.stepForms[0].get('type')?.value;
    return ['sell', 'rent_out'].includes(type);
  }

  getVillageName(): boolean {
    const unitType = this.stepForms[0].get('unitType')?.value;
    const targetUnitTypes = ['vacation_villa', 'chalets'];
    return targetUnitTypes.includes(unitType);
  }

  getSelectedAccessoriesText(): string {
    const input = this.currentInputs.find((i) => i.name === 'otherAccessories');
    if (!input) return '';
    return this.getSelectedText('otherAccessories', input.options);
  }

  isAccessorySelected(accessoryValue: string): boolean {
    return this.isMultiSelectOptionSelected('otherAccessories', accessoryValue);
  }

  toggleAccessory(accessoryValue: string): void {
    this.toggleMultiSelect('otherAccessories', accessoryValue);
  }

  getSelectedOtherExpensesText(): string {
    const input = this.currentInputs.find((i) => i.name === 'otherExpenses');
    if (!input) return '';
    return this.getSelectedText('otherExpenses', input.options);
  }

  isOtherExpenseSelected(expenseValue: string): boolean {
    return this.isMultiSelectOptionSelected('otherExpenses', expenseValue);
  }

  toggleOtherExpense(expenseValue: string): void {
    this.toggleMultiSelect('otherExpenses', expenseValue);
  }

  onAllAccessoriesChange(event: any): void {
    const input = this.currentInputs.find((i) => i.name === 'otherAccessories');
    if (!input) return;
    const isChecked = event.target.checked;
    const updatedValues = isChecked ? input.options.map((opt: any) => opt.value) : [];
    this.stepForms[this.currentStep - 1].patchValue({ otherAccessories: updatedValues });
    this.cdr.markForCheck();
  }

    getSelectedText(fieldName: string, options: any[]): string {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const selectedOptions = options.filter((opt) => currentValues.includes(opt.value));
    if (selectedOptions.length === 0) return '';
    if (selectedOptions.length === 1) return selectedOptions[0].key;
    return `${selectedOptions.length} items selected`;
  }

  isMultiSelectOptionSelected(fieldName: string, value: string): boolean {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    return currentValues.includes(value);
  }

  toggleMultiSelect(fieldName: string, value: string): void {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    let updatedValues;

    if (currentValues.includes(value)) {
      updatedValues = currentValues.filter((item: string) => item !== value);
    } else {
      updatedValues = [...currentValues, value];
    }

    this.getCurrentForm().patchValue({ [fieldName]: updatedValues });
    this.cdr.markForCheck();
  }

  // Enhanced multiselect methods
  getSelectedCount(fieldName: string): number {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    return currentValues.length;
  }

  getSearchQuery(fieldName: string): string {
    return this.searchQueries[fieldName] || '';
  }

  updateSearchQuery(fieldName: string, event: any): void {
    this.searchQueries[fieldName] = event.target.value;
    this.cdr.markForCheck();
  }

  clearSearch(fieldName: string): void {
    this.searchQueries[fieldName] = '';
    this.cdr.markForCheck();
  }

  getFilteredOptions(fieldName: string, options: any[]): any[] {
    const searchQuery = this.getSearchQuery(fieldName).toLowerCase();
    if (!searchQuery) {
      return options;
    }
    return options.filter(option =>
      option.key.toLowerCase().includes(searchQuery)
    );
  }

  areAllOptionsSelected(fieldName: string): boolean {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));
    return visibleOptions.length > 0 && visibleOptions.every(option => currentValues.includes(option.value));
  }

  areSomeOptionsSelected(fieldName: string): boolean {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));
    return visibleOptions.some(option => currentValues.includes(option.value));
  }

  toggleSelectAll(fieldName: string): void {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));

    if (this.areAllOptionsSelected(fieldName)) {
      // Deselect all visible options
      const updatedValues = currentValues.filter((value: string) =>
        !visibleOptions.some(option => option.value === value)
      );
      this.getCurrentForm().patchValue({ [fieldName]: updatedValues });
    } else {
      // Select all visible options
      const newValues = visibleOptions.map(option => option.value);
      const updatedValues = [...new Set([...currentValues, ...newValues])];
      this.getCurrentForm().patchValue({ [fieldName]: updatedValues });
    }
    this.cdr.markForCheck();
  }

  clearAllSelections(fieldName: string): void {
    this.getCurrentForm().patchValue({ [fieldName]: [] });
    this.cdr.markForCheck();
  }

  getSelectedOptions(fieldName: string, options: any[]): any[] {
    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];
    return options.filter(option => currentValues.includes(option.value));
  }

  private getOptionsForField(fieldName: string): any[] {
    const input = this.currentInputs.find(inp => inp.name === fieldName);
    return input?.options || [];
  }

  getText(options: { key: string; value: string }[], value: string): string {
    const item = options.find((item) => item.value === value);
    return item ? item.key : '';
  }

  select(form: FormGroup, field: string, value: string): void {
    form.patchValue({ [field]: value });
    form.get(field)?.markAsTouched();
    form.get(field)?.updateValueAndValidity();
    this.cdr.markForCheck();

    // Special handling for specialization scope changes
    if (field === 'specializationScope') {
      // Reset type and unitType when scope changes since filtered options may change
      form.patchValue({ type: '', unitType: '' });
      form.get('type')?.markAsUntouched();
      form.get('unitType')?.markAsUntouched();
      // Refresh the step inputs to update the unit type options
      this.loadStepInputs();
    } else if (field === 'type') {
      // Reset unitType when type changes
      form.patchValue({ unitType: '' });
      form.get('unitType')?.markAsUntouched();
    }
  }

  onSelectChange(fieldName: string, value: number, name: string): void {
    if (fieldName === 'cityId') {
      this.selectCity(value, name);
    } else if (fieldName === 'areaId') {
      this.selectArea(value, name);
    } else if (fieldName === 'subAreaId') {
      this.selectSubArea(value, name);
    }
  }

  fileValidator(options: { allowedTypes: string[]; maxSize: number }): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const file = control.value;
      if (!file) return null;

      if (file.size > options.maxSize) {
        return { maxSize: true };
      }

      if (!options.allowedTypes.includes(file.type)) {
        return { invalidType: true };
      }

      return null;
    };
  }

  fileArrayValidator(options: { allowedTypes: string[], maxSize: number }): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const files = control.value;

      if (!Array.isArray(files)) {
        return { notArray: true };
      }

      for (const file of files) {
        if (file.size > options.maxSize) {
          return { maxSize: true };
        }
        if (!options.allowedTypes.includes(file.type)) {
          return { invalidType: true };
        }
      }

      return null;
    };
  }

  isClient() {
    return this.userRole == 'client';
  }

  shouldShowLocationSuggestions(): boolean {
    if (!this.isClient()) {
      console.log('shouldShowLocationSuggestions: false - not client');
      return false;
    }

    const scope = this.stepForms[0].get('specializationScope')?.value;
    const type = this.stepForms[0].get('type')?.value;
    console.log('shouldShowLocationSuggestions - scope:', scope, 'type:', type, 'isClient:', this.isClient());

    // Show for inside compound scenarios
    if (this.getInsideCompoundPrivilege()) {
      console.log('shouldShowLocationSuggestions: true - inside compound');
      return true;
    }

    // Show for purchase outside compound scenarios
    if (scope === 'purchase_sell_outside_compound' && type === 'purchasing') {
      console.log('shouldShowLocationSuggestions: true - purchase outside compound');
      return true;
    }

    // Show for rent-in outside compound scenarios (but not rent-out)
    if (scope === 'rentals_outside_compound' && type === 'rent_in') {
      console.log('shouldShowLocationSuggestions: true - rent-in outside compound');
      return true;
    }

    console.log('shouldShowLocationSuggestions: false - no matching condition');
    return false;
  }

  getInsideCompoundPrivilege(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const targetScopes = [
      'primary_inside_compound',
      'resale_inside_compound',
      'rentals_inside_compound',
    ];
    return targetScopes.includes(scope);
  }

  getOutsideCompoundPrivilege(): boolean {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    const targetScopes = [
      'purchase_sell_outside_compound',
      'rentals_outside_compound',
    ];
    return targetScopes.includes(scope);
  }

  selectCity(cityId: number, cityName: string): void {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.getCurrentForm().patchValue({ cityId, areaId: '', subAreaId: '' });
    this.areasSubject.next([]);
    this.subAreasSubject.next([]);
    this.selectedAreaId = null;
    this.selectedAreaName = '';
    this.selectedSubAreaName = '';

    this.propertyService
      .getAreas(cityId)
      .pipe(
        map((response: any) => {
          const areas = response.data || response;
          return areas.map((area: any) => ({
            key: area.name_en,
            value: area.id,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching areas:', error);
          Swal.fire('Error', 'Failed to fetch areas.', 'error');
          return of([]);
        })
      )
      .subscribe((areas) => {
        console.log('Fetched areas:', areas);
        this.areasSubject.next(areas);
        this.cdr.markForCheck();
      });
  }

  selectArea(areaId: number, areaName: string): void {
    this.selectedAreaId = areaId;
    this.selectedAreaName = areaName;
    this.getCurrentForm().patchValue({ areaId, subAreaId: '' });
    this.subAreasSubject.next([]);
    this.selectedSubAreaName = '';

    this.propertyService
      .getSubAreas(areaId)
      .pipe(
        map((response: any) => {
          const subAreas = response.data || response;
          return subAreas.map((subArea: any) => ({
            key: subArea.name_en,
            value: subArea.id,
          }));
        }),
        catchError((error) => {
          console.error('Error fetching sub-areas:', error);
          Swal.fire('Error', 'Failed to fetch sub-areas.', 'error');
          return of([]);
        })
      )
      .subscribe((subAreas) => {
        this.subAreasSubject.next(subAreas);
        this.cdr.markForCheck();
      });
  }

  selectSubArea(subAreaId: number, subAreaName: string): void {
    this.selectedSubAreaName = subAreaName;
    this.getCurrentForm().patchValue({ subAreaId });
    this.cdr.markForCheck();
  }

  getConfigKey(): string {
    const step1Values = this.stepForms[0].value;
    return `${step1Values.specializationScope}_${step1Values.type}_${step1Values.unitType}`;
  }

  getFilteredTypeOptions(): OptionItem[] {
    const scope = this.stepForms[0].get('specializationScope')?.value;
    if (scope?.includes('rentals')) {
      return this.typeOptions.filter((t) => ['rent_out', 'rent_in'].includes(t.value));
    } else if (scope?.includes('purchase_sell') || scope?.includes('primary') || scope?.includes('resale')) {
      return this.typeOptions.filter((t) => ['purchasing', 'sell'].includes(t.value));
    }
    return this.typeOptions;
  }

    getFilteredUnitTypeOptions(): OptionItem[] {
    const scope = this.stepForms[0].get('specializationScope')?.value;

    // If no scope is selected, return empty array
    if (!scope) {
      return [];
    }

    // Inside compound unit types (primary and resale)
    if (scope === 'primary_inside_compound' || scope === 'resale_inside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'studios',
          'penthouses',
          'villas',
          'twin_houses',
          'town_houses',
          'standalone_villas',
          'administrative_units',
          // 'commercial_units',
          'medical_clinics',
          // 'commercial_stores',
          'pharmacies',
          'commercial_administrative_buildings',
          'shops',
        ].includes(unitType.value)
      );
    }

    //  if (selectedScope === 'purchase_sell_outside_compound' ||
    //     // selectedScope === 'purchase_sell_inside_compound' ||
    //     selectedScope === 'primary_inside_compound' ||
    //     selectedScope === 'resale_inside_compound') {
    //    return this.Type.filter(type =>
    //     type.value === 'sell' || type.value === 'purchasing'
    // Outside compound unit types (purchase-sell)
    if (scope === 'purchase_sell_outside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'penthouses',
          'studios',
          'basements',
          'roofs',
          'administrative_units',
          'medical_clinics',
          'pharmacies',
          'commercial_stores',
          'standalone_villas',
          'factory_lands',
          'warehouses',
          'residential_buildings',
          'commercial_administrative_buildings'
        ].includes(unitType.value)
      );
    }

    // Rental unit types (both inside and outside compound)
    if (scope === 'rentals_inside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'studios',
          'penthouses',
          'villas',
          'town_houses',
          'twin_houses',
          // 'basements',
          // 'roofs',
          // 'residential_buildings',
          'standalone_villas',
          'administrative_units',
          'commercial_stores',
          'medical_clinics',
          'pharmacies',
          'commercial_administrative_buildings'
        ].includes(unitType.value)
      );
    }

    if (scope === 'rentals_outside_compound') {
      return this.unitTypeOptions.filter(unitType =>
        [
          'apartments',
          'duplexes',
          'studios',
          'penthouses',
          // 'villas',
          // 'town_houses',
          // 'twin_houses',
          'basements',
          'roofs',
          // 'residential_buildings',
          'standalone_villas',
          'administrative_units',
          'commercial_stores',
          'medical_clinics',
          'pharmacies',
          'factory_lands',
          'warehouses',
          'commercial_administrative_buildings'
        ].includes(unitType.value)
      );
    }

    // For other scopes, return all unit types
    return this.unitTypeOptions;
  }

  getCurrentForm(): FormGroup {
    return this.stepForms[this.currentStep - 1];
  }

  nextStep(): void {
    if (this.getCurrentForm().valid && this.currentStep < this.totalSteps) {
      this.currentStep++;
      this.loadStepInputs();
    }
  }

  prevStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
      this.loadStepInputs();
    }
  }

  // Check if current step has inputs
  hasStepInputs(): boolean {
    return this.currentInputs && this.currentInputs.length > 0;
  }

  // Check if input is required
  isInputRequired(input: any): boolean {
    return input.validators && input.validators.some((validator: any) =>
      validator === Validators.required ||
      (validator && validator.toString && validator.toString().includes('required'))
    );
  }

  // Get step 4 info message
  getStep4InfoMessage(): string {
    const configKey = this.getConfigKey();
    if (configKey.includes('rent_in')) {
      return 'No image uploads are required for rent-in requests.';
    }
    return 'No image uploads are available for this property type.';
  }

  trackByInputName(index: number, input: any): string {
    return input.name;
  }

  navigateToErrorStep(stepNumber: number): void {
    this.currentStep = stepNumber;
    this.loadStepInputs();
    this.cdr.markForCheck();
  }

  clearValidationErrors(): void {
    this.validationErrors = [];
    this.showErrorList = false;
    this.cdr.markForCheck();
  }

  /**
   * Get total count of all validation errors across all steps
   */
  getTotalErrorCount(): number {
    return this.validationErrors.reduce((total, stepError) => {
      return total + stepError.errors.length;
    }, 0);
  }

  /**
   * Navigate to the first step that has validation errors
   */
  navigateToFirstErrorStep(): void {
    if (this.validationErrors.length > 0) {
      const firstErrorStep = Math.min(...this.validationErrors.map(error => error.step));
      this.navigateToErrorStep(firstErrorStep);
    }
  }

  isCurrentFormValid(): boolean {
    return this.getCurrentForm().valid;
  }

  // Debug method to show current form errors
  getCurrentFormErrors(): string {
    const form = this.getCurrentForm();
    const errors: string[] = [];

    Object.keys(form.controls).forEach(key => {
      const control = form.get(key);
      if (control && control.invalid) {
        const controlErrors = control.errors;
        if (controlErrors) {
          Object.keys(controlErrors).forEach(errorKey => {
            errors.push(`${key}: ${errorKey}`);
          });
        }
      }
    });

    return errors.length > 0 ? errors.join(', ') : 'No errors';
  }

  getFileCount(fieldName: string): number {
    const files = this.getCurrentForm().get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  onFileChange(event: any, fieldName: string): void {
    if (event.target.files?.length) {
      this.stepForms[this.currentStep - 1].patchValue({
        [fieldName]: Array.from(event.target.files)
      });
      this.cdr.markForCheck();
    }
  }

  submitForm(): void {
    if (this.stepForms.every((form) => form.valid)) {
      this.isSubmitting = true;
      const formData = new FormData();
      formData.append('userId', this.userId.toString());

      // Add step 1 values
      const step1Values = this.stepForms[0].value;
      Object.keys(step1Values).forEach((key) => {
        formData.append(key, step1Values[key] || '');
      });

      // Add location data from step 2
      const step2Values = this.stepForms[1].value;
      if (step2Values.cityId) {
        formData.append('locations[0][city]', step2Values.cityId.toString());
      }
      if (step2Values.areaId) {
        formData.append('locations[0][areas][0][id]', step2Values.areaId.toString());
      }
      if (step2Values.subAreaId) {
        formData.append('locations[0][areas][0][subAreas][0]', step2Values.subAreaId.toString());
      }

      // Add other form values
      this.stepForms.slice(1).forEach((form) => {
        Object.keys(form.value).forEach((key) => {
          if (['cityId', 'areaId', 'subAreaId'].includes(key)) {
            return;
          }
          const value = form.value[key];
          if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
              value.forEach((item: any, i: number) =>
                formData.append(`attributes[${key}][${i}]`, item)
              );
            } else if (typeof value === 'boolean') {
              formData.append(`attributes[${key}]`, value ? '1' : '0');
            } else if (value !== '') {
              formData.append(`attributes[${key}]`, value.toString());
            }
          }
        });
      });

      // Handle file uploads
      const fileFields = ['mainImage', 'galleryImages', 'video', 'unitInMasterPlanImage'];
      fileFields.forEach((field) => {
        const files = this.stepForms[3]?.get(field)?.value;
        if (files && Array.isArray(files) && files.length > 0) {
          const isMultiple = ['galleryImages', 'video'].includes(field);

          if (isMultiple) {
            files.forEach((file: File, index: number) => {
              if (file instanceof File) {
                formData.append(`attributes[${field}][${index}]`, file);
              }
            });
          } else {
            if (files[0] instanceof File) {
              formData.append(`attributes[${field}]`, files[0]);
            }
          }
        }
      });

      this.createRequestService.createRequest(formData).subscribe({
        next: (res) => {
          this.isSubmitting = false;
          Swal.fire('Success', 'Request created successfully!', 'success').then(() => {
            this.router.navigate([`/requests/assign-to`, res.data?.id || res.id]);
          });
        },
        error: (err) => {
          this.isSubmitting = false;
          this.handleSubmissionError(err);
        },
      });
    }
  }

  handleSubmissionError(error: any): void {
    this.validationErrors = [];
    if (error?.error?.errors) {
      Object.keys(error.error.errors).forEach((field) => {
        const cleanField = field.replace(/^attributes\./g, '').replace(/\[\d+\].*/g, '');
        const step = this.currentInputs.find((input) => input.name === cleanField)?.step || 1;
        let stepGroup = this.validationErrors.find((v) => v.step === step);
        if (!stepGroup) {
          stepGroup = {
            step,
            stepName: this.stepNames[step] || `Step ${step}`,
            errors: []
          };
          this.validationErrors.push(stepGroup);
        }
        stepGroup.errors.push({
          field: cleanField,
          messages: Array.isArray(error.error.errors[field])
            ? error.error.errors[field]
            : [error.error.errors[field]],
        });
      });
      this.showErrorList = true;
      this.cdr.markForCheck();
    } else {
      Swal.fire('Error', error?.error?.message || 'An unknown error occurred.', 'error');
    }
  }
}
