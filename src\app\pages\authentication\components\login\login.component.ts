import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthenticationService } from '../../services/authentication.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  currentStep = 1;
  loginForm: FormGroup;
  forgotPasswordForm: FormGroup;
  resetPasswordForm: FormGroup;
  isLoading = false;
  verificationDigits: string[] = ['', '', '', '', ''];

  countdown: number = 25;
  showResendButton: boolean = false;

  // Loading states
  isLoadingLogin: boolean = false;
  isLoadingSendOtp: boolean = false;
  isLoadingCheckOtp: boolean = false;
  isLoadingResetPassword: boolean = false;

  // Error messages
  loginErrorMessage: string = '';
  otpErrorMessage: string = '';
  resetPasswordErrorMessage: string = '';

  // Validators
  static phonePattern = Validators.pattern(/^(01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/);
  static emailOrPhonePattern = Validators.pattern(/^([^\s@]+@[^\s@]+\.[^\s@]+|01[0125]\d{8}|05\d{8}|\+201[0125]\d{8}|\+9665\d{8})$/);
  static passwordPattern = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/);

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authenticationService: AuthenticationService,
    private cd: ChangeDetectorRef
  ) {
    this.loginForm = this.createLoginForm();
    this.forgotPasswordForm = this.createForgotPasswordForm();
    this.resetPasswordForm = this.createResetPasswordForm();
  }

  private createLoginForm(): FormGroup {
    return this.fb.group({
      phone: ['', [Validators.required, LoginComponent.phonePattern]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false],
    });
  }

  private createForgotPasswordForm(): FormGroup {
    return this.fb.group({
      input: ['', [Validators.required, LoginComponent.emailOrPhonePattern]],
      verificationCode: this.fb.array(
        Array(5)
          .fill('')
          .map(() =>
            this.fb.control('', [
              Validators.required,
              Validators.pattern('[0-9]'),
            ])
          )
      ),
    });
  }

  private createResetPasswordForm(): FormGroup {
    return this.fb.group({
      newPassword: ['', [Validators.required, Validators.minLength(8), LoginComponent.passwordPattern]],
      password_confirmation: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {}

  // Helper methods for validation
  isFieldInvalid(fieldName: string, formGroup?: FormGroup): boolean {
    const form = formGroup || this.loginForm;
    const field = form.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  markFieldAsTouched(fieldName: string, formGroup?: FormGroup): void {
    const form = formGroup || this.loginForm;
    form.get(fieldName)?.markAsTouched();
  }

  getFieldError(fieldName: string, formGroup?: FormGroup): string {
    const form = formGroup || this.loginForm;
    const field = form.get(fieldName);
    if (!field?.errors) return '';

    const errors = field.errors;
    if (errors['required']) return 'This field is required';
    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';
    if (errors['pattern'] && fieldName === 'input') return 'Enter valid email or phone number';
    if (errors['pattern'] && fieldName === 'newPassword') return 'Need uppercase, lowercase & number';
    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;

    return 'Invalid input';
  }

  getFormError(formGroup: FormGroup): string {
    const password = formGroup.get('newPassword')?.value;
    const confirmPassword = formGroup.get('password_confirmation')?.value;

    if (password && confirmPassword && password !== confirmPassword) {
      return 'Passwords do not match';
    }
    return '';
  }

  // Check if forms are valid
  isLoginFormValid(): boolean {
    return this.loginForm.valid;
  }

  isForgotPasswordFormValid(): boolean {
    const input = this.forgotPasswordForm.get('input');
    return !!input?.valid;
  }

  isVerificationCodeValid(): boolean {
    const verificationCode = this.forgotPasswordForm.get('verificationCode') as FormArray;
    return verificationCode.valid;
  }

  isResetPasswordFormValid(): boolean {
    const newPassword = this.resetPasswordForm.get('newPassword');
    const passwordConfirmation = this.resetPasswordForm.get('password_confirmation');
    const passwordsMatch = newPassword?.value === passwordConfirmation?.value;

    return !!(newPassword?.valid && passwordConfirmation?.valid && passwordsMatch);
  }

  login(): void {
    if (!this.isLoginFormValid()) {
      this.markFieldAsTouched('phone');
      this.markFieldAsTouched('password');
      return;
    }

    this.isLoadingLogin = true;
    this.loginErrorMessage = '';
    let params = this.loginForm.value;

    this.authenticationService.login(params).subscribe({
      next: (response: any) => {
        let user = response.data;
        localStorage.setItem('authToken', user.authToken);
        this.authenticationService.setCurrentUser(response.data);
        console.log('login successfully:', response);
        this.isLoadingLogin = false;

        if (user.role == 'developer') {
          this.router.navigate(['/developer/dashboards']);
        } else if (user.role == 'broker') {
          this.router.navigate(['/broker/dashboard']);
        } else if (user.role == 'client') {
          this.router.navigate(['/requests']);
        } else if (user.role == 'admin') {
          this.router.navigate(['/super-admin/dashboard']);
        }
        this.cd.markForCheck();
      },
      error: (error: any) => {
        console.error('Failed to login:', error);
        this.isLoadingLogin = false;
        // this.loginErrorMessage = error?.error?.message || 'Login failed. Please check your credentials.';
        this.loginErrorMessage = 'Login failed. Please check your credentials.';
        this.cd.markForCheck();
      }
    });
  }

  get verificationCodeControls() {
    return (this.forgotPasswordForm.get('verificationCode') as FormArray)
      .controls;
  }

  goToForgotPassword(): void {
    this.currentStep = 2;
  }
  backToLogin(): void {
    this.currentStep = 1;
  }
  backToForgotPassword(): void {
    this.currentStep = 2;
  }
  backToVerificationCode(): void {
    this.currentStep = 3;
  }

  nextStep(): void {
    if (this.currentStep < 4) {
      // Clear error messages when moving between steps
      if (this.currentStep === 2) {
        this.otpErrorMessage = '';
      } else if (this.currentStep === 3) {
        this.otpErrorMessage = '';
      }
      this.currentStep++;
    }
  }

  submitResetPassword() {
    if (!this.isResetPasswordFormValid()) {
      this.markFieldAsTouched('newPassword', this.resetPasswordForm);
      this.markFieldAsTouched('password_confirmation', this.resetPasswordForm);
      return;
    }

    this.isLoadingResetPassword = true;
    this.resetPasswordErrorMessage = '';
    const input = this.forgotPasswordForm.get('input')?.value?.trim();

    let params: {
      email?: string;
      phone?: string;
      password?: string;
      password_confirmation?: string;
    } = {};
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const isPhone = /^[0-9+\-\s]{7,15}$/.test(input);

    if (isEmail) {
      params.email = input;
    } else if (isPhone) {
      params.phone = input;
    }
    params.password = this.resetPasswordForm.get('newPassword')?.value?.trim();
    params.password_confirmation = this.resetPasswordForm
      .get('password_confirmation')
      ?.value?.trim();

    this.authenticationService.resetPassword(params).subscribe({
      next: async (response) => {
        console.log('reset password success:', response);
        this.isLoadingResetPassword = false;
        // Navigate back to login instead of reloading
        this.currentStep = 1;
        this.loginErrorMessage = 'Password reset successfully! Please login with your new password.';
        this.cd.markForCheck();
      },
      error: (err) => {
        console.error('Failed to reset:', err);
        this.isLoadingResetPassword = false;
        this.resetPasswordErrorMessage = 'Failed to reset password. Please try again.';
        // this.resetPasswordErrorMessage = err?.error?.message || 'Failed to reset password. Please try again.';
        this.cd.markForCheck();
      },
    });
  }

  private clearOtpInputs() {
    this.verificationDigits = ['', '', '', '', ''];
    const verificationCodeArray = this.forgotPasswordForm.get('verificationCode') as FormArray;
    verificationCodeArray.controls.forEach((control) => {
      control.setValue('');
      control.markAsUntouched();
      control.markAsPristine();
    });
  }

  handleNextStepAndSendCode(): void {
    this.sendVerificationCode(true);
  }

  sendVerificationCode(moveToNextStep: boolean = false) {
    if (!this.isForgotPasswordFormValid()) {
      this.markFieldAsTouched('input', this.forgotPasswordForm);
      return;
    }

    this.isLoadingSendOtp = true;
    this.otpErrorMessage = '';
    const input = this.forgotPasswordForm.get('input')?.value?.trim();

    let params: { email?: string; phone?: string } = {};
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const isPhone = /^[0-9+\-\s]{7,15}$/.test(input);

    if (isEmail) {
      params.email = input;
    } else if (isPhone) {
      params.phone = input;
    }

    this.authenticationService.sendOtp(params).subscribe({
      next: (response: any) => {
        console.log('OTP sent:', response);
        this.isLoadingSendOtp = false;
        this.startCountdown();
        if (moveToNextStep) {
          this.nextStep();
        }
        this.cd.markForCheck();
      },
      error: (error: any) => {
        console.error('Failed to send OTP:', error);
        this.isLoadingSendOtp = false;
        this.otpErrorMessage = error?.error?.message || 'Failed to send verification code. Please try again.';
        this.cd.markForCheck();
      }
    });
  }

  checkOTP() {
    if (!this.isVerificationCodeValid()) {
      const verificationCodeArray = this.forgotPasswordForm.get('verificationCode') as FormArray;
      verificationCodeArray.controls.forEach(control => control.markAsTouched());
      return;
    }

    this.isLoadingCheckOtp = true;
    this.otpErrorMessage = '';
    const input = this.forgotPasswordForm.get('input')?.value?.trim();
    const codeArray = this.forgotPasswordForm.get('verificationCode')?.value;
    const otp = codeArray.join('');

    let params: { email?: string; phone?: string; otp?: number } = {};
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    const isPhone = /^[0-9+\-\s]{7,15}$/.test(input);

    if (isEmail) {
      params.email = input;
    } else if (isPhone) {
      params.phone = input;
    }

    params.otp = otp;

    this.authenticationService.checkOtp(params).subscribe({
      next: (response: any) => {
        console.log('OTP checked:', response);
        this.isLoadingCheckOtp = false;
        this.nextStep();
        this.cd.markForCheck();
      },
      error: (error: any) => {
        console.error('Failed to check OTP:', error);
        this.isLoadingCheckOtp = false;
        this.otpErrorMessage = error?.error?.message || 'Invalid verification code. Please try again.';
        this.cd.markForCheck();
      }
    });
  }

  startCountdown() {
    this.showResendButton = false;
    this.countdown = 25;

    const intervalId = setInterval(() => {
      this.countdown--;
      if (this.countdown === 0) {
        clearInterval(intervalId);
        this.showResendButton = true;
      }
      this.cd.markForCheck();
    }, 1000);
  }

  autoFocusNext(event: any, index: number): void {
    const input = event.target;
    if (input.value && index < 5) {
      const nextInput =
        input.parentElement?.nextElementSibling?.querySelector('input');
      nextInput?.focus();
    }
  }

  onResendCode() {
    this.clearOtpInputs();
    this.otpErrorMessage = '';
    this.sendVerificationCode(false);
  }
}
