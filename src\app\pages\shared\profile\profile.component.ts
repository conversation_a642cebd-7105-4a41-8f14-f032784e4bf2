import { ChangeDetectorRef, Component, OnInit } from '@angular/core';

import Swal from 'sweetalert2';
// import { BrokerService } from '../../broker/services/broker.service';
// import { AccountTypeMapper } from '../../broker/account-type-mapper';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/_metronic/shared/shared.module';

// Import child components
import { ProfileHeaderComponent } from './components/profile-header/profile-header.component';
import { ProfileDetailsComponent } from './components/profile-details/profile-details.component';
import { SignInMethodComponent } from './components/sign-in-method/sign-in-method.component';
import { AdvertisementsDetailsComponent } from './components/advertisements-details/advertisements-details.component';
import { AccountDetailsComponent } from './components/account-details/account-details.component';
import { ProfileService } from './services/profile.service';
import { BaseLoading } from '../base-loading/base-loading';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SharedModule,
    ProfileHeaderComponent,
    ProfileDetailsComponent,
    SignInMethodComponent,
    AdvertisementsDetailsComponent,
    AccountDetailsComponent,
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
})
export class ProfileComponent extends BaseLoading implements OnInit {
  user: any = {};

  constructor(
    private cd: ChangeDetectorRef,
    private profileService: ProfileService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }
  /***************************************************** */

  loadUserProfile() {
    // Start loading
    this.isLoading = true;

    this.profileService.getCurrentUserProfile().subscribe({
      next: (response: any) => {
        this.user = response.data;
        this.isLoading = false; // Stop loading on success
        this.cd.detectChanges();
      },
      error: (error: any) => {
        this.isLoading = false; // Stop loading on error

        // Format error message
        let errorMessage = 'Failed to load profile data.';
        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          title: 'Error!',
          text: errorMessage,
          icon: 'error',
          confirmButtonText: 'OK'
        });
      },
    });
  }

  /************************************************************* */

  /************************************************************* */

  // Account details
  accountTitle: string = 'Account Details';
}
