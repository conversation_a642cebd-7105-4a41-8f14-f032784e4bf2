import { Component, Input, ChangeDetectorRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormGroup,
  FormsModule,
  FormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { Modal } from 'bootstrap';
import { ProfileService } from '../../services/profile.service';
import Swal from 'sweetalert2';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

// Define interfaces for the tree structure

@Component({
  selector: 'app-advertisements-details',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './advertisements-details.component.html',
  styleUrl: './advertisements-details.component.scss',
})
export class AdvertisementsDetailsComponent implements OnInit {
  @Input() user: any = {};
  cities: any[] = [];
  areas: any[] = [];
  selectedCityId: any;
  selectedCityName: string;
  selectedAreaName: string;
  isLoadingCities = false;

  Form: FormGroup;

  // New items for the modal forms
  newLocation: string = '';
  newSpecialization: string = '';

  // References to the modals
  private locationModal: Modal | null = null;
  private specializationModal: Modal | null = null;

  constructor(
    private profileService: ProfileService,
    private propertyService: PropertyService,
    private cd: ChangeDetectorRef,
    private fb: FormBuilder
  ) {
    // Initialize the form
    this.Form = this.fb.group({
      cityId: [''],
      areaId: [''],
    });
  }

  ngOnInit(): void {
    this.updatedScopes = [...(this.user.specializationScopes || [])];
    if (this.user && this.user.locations) {
      this.locations = this.user.locations;
    }
    // Load cities when component initializes
    this.loadCities();
  }

  //***************************************************** */
  //***************************************************** */
  // ******************location modal***********************

  // Current selected locations
  locations: any[] = [];

  openLocationModal() {
    this.newLocation = '';
    const modalElement = document.getElementById('addLocationModal');
    if (modalElement) {
      this.locationModal = new Modal(modalElement);
      this.locationModal.show();
    }
  }

  saveLocation() {
    const selectedAreaId = this.Form.value.areaId;
    const selectedArea = this.areas.find((area) => area.id === selectedAreaId);

    const existingAreaIds = this.user.areas.map((area: any) => area.id);

    if (!existingAreaIds.includes(selectedAreaId)) {
      existingAreaIds.push(selectedAreaId);
    }

    this.profileService
      .updateAdvertisements(this.user.id, { areaIds: existingAreaIds })
      .subscribe({
        next: (response) => {
          // console.log(locationData);
          console.log(response);
          this.showSuccessMessage('Location saved successfully');

          if (
            !this.user.areas.some((area: any) => area.id === selectedAreaId)
          ) {
            this.user.areas.push({
              id: selectedAreaId,
              name_en: selectedArea
                ? selectedArea.name_en
                : this.selectedAreaName,
            });
          }

          if (this.locationModal) {
            this.locationModal.hide();
          }
          this.cd.detectChanges();
        },
        error: (err) => {
          this.showErrorMessage(err);
        },
      });
  }

  loadCities(): void {
    this.isLoadingCities = true;
    this.propertyService.getCities().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.isLoadingCities = false;
        this.cd.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cd.detectChanges();
      },
    });
  }
  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;

    // Check if Form is initialized before patching
    if (this.Form) {
      this.Form.patchValue({
        cityId: cityId,
      });
    }

    // Load areas for the selected city
    this.loadAreas(cityId);
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;

    // Check if Form is initialized before patching
    if (this.Form) {
      this.Form.patchValue({
        areaId: areaId,
      });
    }
  }
  removeLocation(index: number, areaId: number) {
    if (index >= 0 && index < this.user.areas.length) {
      let currentAreaIds = Array.isArray(this.Form.value.areaId)
        ? this.Form.value.areaId
        : [this.Form.value.areaId];

      let updatedAreaIds = currentAreaIds.filter((id: number) => id !== areaId);
      this.Form.patchValue({ areaId: updatedAreaIds });
      this.user.areas.splice(index, 1);
      this.openLocationModal();

    }
  }
  //***************************************************** */
  //***************************************************** */
  // ****************specialization modal***********************

  searchTerm: string = '';
  formChanged: boolean = false;
  originalSpecializations: any[] = [];
  updatedScopes: any[] = [];

  staticScopes = [
    {
      specialization_scope: 'purchase_sell_outside_compound',
      specializations: [
        'purchasing_sell_residential_outside_compound',
        'purchasing_sell_national_housing_projects_outside_compound',
        'purchasing_sell_administrative_commercial_units_outside_compound',
        'purchasing_sell_industrial_and_warehousing_outside_compound',
        'purchasing_sell_lands_and_ready_projects_outside_compound',
        'purchasing_sell_villas_and_buildings_outside_compound'
      ],
      expanded: false,
      selected: false,
    },
    // {
    //   specialization_scope: 'purchase_sell_inside_compound',
    //   specializations: [
    //     'purchasing_sell_residential_inside_compound',
    //     'purchasing_sell_villas_inside_compound',
    //     'purchasing_sell_administrative_commercial_units_inside_compound'
    //   ],
    //   expanded: false,
    //   selected: false,
    // },
    {
      specialization_scope: 'primary_inside_compound',
      specializations: [
        'purchasing_sell_residential_inside_compound',
        'purchasing_sell_villas_inside_compound',
        'purchasing_sell_administrative_commercial_units_inside_compound'
      ],
      expanded: false,
      selected: false,
    },
    {
      specialization_scope: 'resale_inside_compound',
      specializations: [
        'purchasing_sell_residential_inside_compound',
        'purchasing_sell_villas_inside_compound',
        'purchasing_sell_administrative_commercial_units_inside_compound'
      ],
      expanded: false,
      selected: false,
    },
    {
      specialization_scope: 'rentals_outside_compound',
      specializations: [
        'rent_residential_inside_compound',
        'rent_hotel_Units_inside_compound',
        'rent_administrative_commercial_units_inside_compound'
      ],
      expanded: false,
      selected: false,
    },
    {
      specialization_scope: 'rentals_inside_compound',
      specializations: [
        'rent_residential_outside_compound',
        'rent_national_housing_projects_compound',
        'rent_administrative_commercial_units_outside_compound',
        'rent_industrial_and_warehousing_outside_compound',
        'rent_hotel_units_outside_compound'
      ],
      expanded: false,
      selected: false,
    },
  ];

  // Open specialization modal
  openSpecializationModal() {
     this.searchTerm = '';
     const modalElement = document.getElementById('addSpecializationModal');
    if (modalElement) {
      this.specializationModal = new Modal(modalElement);
      this.specializationModal.show();
    }
  }

  hasScope(scopeName: string): boolean {
    return this.updatedScopes.some(
      (item) => item.specialization_scope === scopeName
    );
  }

  hasSpecialization(scopeName: string, specialization: string): boolean {
    if (!this.user || !this.user.specializationScopes) return false;

    return this.user.specializationScopes.some(
      (item: any) =>
        item.specialization_scope === scopeName &&
        item.specialization === specialization
    );
  }

  onScopeChange(scopeName: string, specialization: string, event: Event): void {
    console.log(specialization);
    const checked = (event.target as HTMLInputElement).checked;
    if (checked) {
      const exists = this.updatedScopes.some(
        (item) =>
          item.specialization_scope === scopeName &&
          item.specialization === specialization
      );

      if (!exists) {
        this.updatedScopes.push({
          specialization_scope: scopeName,
          specialization: specialization,
        });
      }
    } else {
      this.updatedScopes = this.updatedScopes.filter(
        (item) =>
          !(
            item.specialization_scope === scopeName &&
            item.specialization === specialization
          )
      );
    }

    this.formChanged = true;
  }

  saveChanges() {
    let payload: any = { specializationScopes: {} };

    console.log('Updated scopes:', this.updatedScopes);

    this.updatedScopes.forEach((scope) => {
      const key = scope.specialization_scope;
      if (!key) return;
      if (!payload.specializationScopes[key]) {
        payload.specializationScopes[key] = [];
      }

      payload.specializationScopes[key].push(scope.specialization);
    });

    console.log('Payload:', payload);

    this.profileService.updateAdvertisements(this.user.id, payload).subscribe({
      next: (res) => {
        this.formChanged = false;
        console.log('Payload:', payload);
        console.log('Response:', res);

        this.showSuccessMessage('Saved successfully');
        this.specializationModal?.hide();
      },
      error: (err) => {
        this.showErrorMessage(err);
      },
    });
  }

  private showSuccessMessage(message: string): void {
    Swal.fire({
      icon: 'success',
      title: 'Success',
      text: message,
    });
  }

  private showErrorMessage(error: any): void {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: error.error.message,
    });
  }

  specializationDisplayMap: { [key: string]: string } = {
    'purchasing_sell_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - studio',
    'purchasing_sell_national_housing_projects_outside_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
    'purchasing_sell_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
    'purchasing_sell_industrial_and_warehousing_outside_compound': 'Factories - Warehouses - Industrial Lands - Warehouse Lands',
    'purchasing_sell_lands_and_ready_projects_outside_compound': 'Administrative & Commercial Lands - Commercial Administrative Malls',
    'purchasing_sell_villas_and_buildings_outside_compound': 'Villas - Full Buildings - Residential Lands - Concrete Structure',
    'purchasing_sell_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - studio',
    'purchasing_sell_villas_inside_compound': 'Villas - Standalone - town house - twin house',
    'purchasing_sell_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
    'rent_residential_inside_compound': 'Apartments - duplexes - penthouses - i villa - villas - studio',
    'rent_hotel_Units_inside_compound': 'Hotel Units',
    'rent_administrative_commercial_units_inside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops',
    'rent_residential_outside_compound': 'Apartments - duplexes - penthouses - roof - basements - villas - studio',
    'rent_national_housing_projects_compound': 'National Housing - Mixed Housing - Youth Housing - Ganet Masr - Dar Masr - Sakan Masr',
    'rent_administrative_commercial_units_outside_compound': 'Administrative Units - Commercial - Pharmacies - Clinics - Shops - full buildings',
    'rent_industrial_and_warehousing_outside_compound': 'Factories - Warehouses',
    'rent_hotel_units_outside_compound': 'Hotel Units'
};

}
